import { db } from "@/lib/db";
import { uploadToR2, getR2PublicUrl } from "@/lib/r2";
import { validatePolicyDocument } from "@/lib/file-validation";
import { sanitizeFilename } from "@/lib/utils";
import { DocumentationType } from "@prisma/client";
import { GoogleGenerativeAI, Part, GenerationConfig } from "@google/generative-ai";
import { zodToJsonSchema } from "zod-to-json-schema";
import { z } from "zod";

// Initialize the Gemini API client
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");

const ValidationSchema = z.object({
  isValid: z
    .boolean()
    .describe("Indicates if the document is a valid insurance policy."),
  reason: z.string().describe("A concise reason for the validation decision."),
});

export interface PolicyUploadOptions {
  file: File;
  fileName?: string;
  accountHolderId: string;
  documentType: DocumentationType;
  relatedAuctionId?: string;
  isPolicyAttested?: boolean;
  location?: string;
  skipAIValidation?: boolean; // For cases where AI validation is not needed
}

export interface PolicyUploadResult {
  documentation: {
    id: string;
    fileName: string | null;
    url: string;
    fileSize: number;
    mimeType: string;
    type: DocumentationType;
    accountHolderId: string;
    relatedAuctionId?: string | null;
    isPolicyAttested?: boolean;
    policyAttestedAt?: Date | null;
  };
  r2Key: string;
  publicUrl: string;
}

/**
 * Unified service for handling policy file uploads to Cloudflare R2
 * Used by both new policy creation and auction policy upload flows
 */
export class PolicyUploadService {
  /**
   * Validates a policy document using AI extraction
   * @param file - The file to validate
   * @returns Validation result
   */
  static async validatePolicyWithAI(file: File): Promise<{ isValid: boolean; reason?: string; errorCode?: string }> {
    try {
      const modelName = process.env.GEMINI_MODEL || "gemini-1.5-flash";
      const model = genAI.getGenerativeModel({ model: modelName });

      const generationConfig: GenerationConfig = {
        responseMimeType: "application/json",
      };

      const validationJsonSchema = zodToJsonSchema(ValidationSchema, "validationSchema");

      const fileBuffer = Buffer.from(await file.arrayBuffer());
      const filePart: Part = {
        inlineData: {
          data: fileBuffer.toString("base64"),
          mimeType: file.type,
        },
      };

      const validationPrompt = `
        You are a document validator for an insurance platform. Your task is to determine if the uploaded document is a valid insurance policy.

        A valid insurance policy should contain:
        - Policy number or reference
        - Insurance company name
        - Policy holder information
        - Coverage details or insured items
        - Policy dates (start/end dates)
        - Premium or payment information

        Analyze the document and determine if it's a legitimate insurance policy document.

        Return your response in the following JSON format:
        {
          "isValid": boolean,
          "reason": "Brief explanation of your decision"
        }
      `;

      const validationResult = await model.generateContent({
        contents: [{ role: "user", parts: [{ text: validationPrompt }, filePart] }],
        generationConfig: {
          ...generationConfig,
          responseSchema: validationJsonSchema.definitions?.validationSchema,
        },
      });

      const validationText = validationResult.response.text();
      const validation = JSON.parse(validationText);

      if (!validation.isValid) {
        return {
          isValid: false,
          reason: validation.reason,
          errorCode: "UNSUPPORTED_DOCUMENT_TYPE"
        };
      }

      return { isValid: true };
    } catch (error) {
      console.error("AI validation error:", error);
      return {
        isValid: false,
        reason: "Error during document validation",
        errorCode: "GENERIC_VALIDATION_FAILURE"
      };
    }
  }

  /**
   * Uploads a policy file to R2 and creates a documentation record
   * @param options - Upload configuration options
   * @returns Upload result with documentation record and URLs
   */
  static async uploadPolicyFile(options: PolicyUploadOptions): Promise<PolicyUploadResult> {
    const {
      file,
      fileName,
      accountHolderId,
      documentType,
      relatedAuctionId,
      isPolicyAttested = false,
      location = "policies",
      skipAIValidation = false
    } = options;

    // Validate the file using centralized utility
    validatePolicyDocument(file);

    // AI validation for policy documents (MVP with manual validation)
    if (!skipAIValidation) {
      const aiValidation = await this.validatePolicyWithAI(file);
      if (!aiValidation.isValid) {
        throw new Error(`Document validation failed: ${aiValidation.reason}`);
      }
    }

    // Sanitize filename
    const sanitizedFilename = fileName ? sanitizeFilename(fileName) : sanitizeFilename(file.name);

    // Upload file to Cloudflare R2
    const r2Key = await uploadToR2(file, `${accountHolderId}/${location}`);
    const publicUrl = getR2PublicUrl(r2Key);

    // Create documentation record
    const documentation = await db.documentation.create({
      data: {
        fileName: sanitizedFilename,
        url: publicUrl,
        fileSize: file.size,
        mimeType: file.type,
        type: documentType,
        accountHolderId,
        relatedAuctionId,
        isPolicyAttested,
        policyAttestedAt: isPolicyAttested ? new Date() : null,
      },
    });

    return {
      documentation,
      r2Key,
      publicUrl,
    };
  }

  /**
   * Uploads a new policy document for auction completion
   * @param file - The policy file to upload
   * @param fileName - Optional custom filename
   * @param accountHolderId - Account holder ID
   * @param auctionId - Related auction ID
   * @returns Upload result
   */
  static async uploadAuctionPolicyFile(
    file: File,
    fileName: string | undefined,
    accountHolderId: string,
    auctionId: string
  ): Promise<PolicyUploadResult> {
    return this.uploadPolicyFile({
      file,
      fileName,
      accountHolderId,
      documentType: "NEW_POLICY_DOCUMENT",
      relatedAuctionId: auctionId,
      isPolicyAttested: true,
      location: "policies",
      skipAIValidation: false // Enable AI validation for auction uploads
    });
  }

  /**
   * Uploads a policy document for new policy creation
   * @param file - The policy file to upload
   * @param fileName - Optional custom filename
   * @param accountHolderId - Account holder ID
   * @returns Upload result
   */
  static async uploadNewPolicyFile(
    file: File,
    fileName: string | undefined,
    accountHolderId: string
  ): Promise<PolicyUploadResult> {
    return this.uploadPolicyFile({
      file,
      fileName,
      accountHolderId,
      documentType: "POLICY_DOCUMENT",
      isPolicyAttested: false,
      location: "policies",
      skipAIValidation: false // Enable AI validation for new policy uploads
    });
  }
}
