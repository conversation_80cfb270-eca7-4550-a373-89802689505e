import { db } from "@/lib/db";
import { uploadToR2, getR2PublicUrl } from "@/lib/r2";
import { validatePolicyDocument } from "@/lib/file-validation";
import { sanitizeFilename } from "@/lib/utils";
import { DocumentationType } from "@prisma/client";

export interface PolicyUploadOptions {
  file: File;
  fileName?: string;
  accountHolderId: string;
  documentType: DocumentationType;
  relatedAuctionId?: string;
  isPolicyAttested?: boolean;
  location?: string;
}

export interface PolicyUploadResult {
  documentation: {
    id: string;
    fileName: string | null;
    url: string;
    fileSize: number;
    mimeType: string;
    type: DocumentationType;
    accountHolderId: string;
    relatedAuctionId?: string | null;
    isPolicyAttested?: boolean;
    policyAttestedAt?: Date | null;
  };
  r2Key: string;
  publicUrl: string;
}

/**
 * Unified service for handling policy file uploads to Cloudflare R2
 * Used by both new policy creation and auction policy upload flows
 */
export class PolicyUploadService {
  /**
   * Uploads a policy file to R2 and creates a documentation record
   * @param options - Upload configuration options
   * @returns Upload result with documentation record and URLs
   */
  static async uploadPolicyFile(options: PolicyUploadOptions): Promise<PolicyUploadResult> {
    const {
      file,
      fileName,
      accountHolderId,
      documentType,
      relatedAuctionId,
      isPolicyAttested = false,
      location = "policies"
    } = options;

    // Validate the file using centralized utility
    validatePolicyDocument(file);

    // Sanitize filename
    const sanitizedFilename = fileName ? sanitizeFilename(fileName) : sanitizeFilename(file.name);

    // Upload file to Cloudflare R2
    const r2Key = await uploadToR2(file, `${accountHolderId}/${location}`);
    const publicUrl = getR2PublicUrl(r2Key);

    // Create documentation record
    const documentation = await db.documentation.create({
      data: {
        fileName: sanitizedFilename,
        url: publicUrl,
        fileSize: file.size,
        mimeType: file.type,
        type: documentType,
        accountHolderId,
        relatedAuctionId,
        isPolicyAttested,
        policyAttestedAt: isPolicyAttested ? new Date() : null,
      },
    });

    return {
      documentation,
      r2Key,
      publicUrl,
    };
  }

  /**
   * Uploads a new policy document for auction completion
   * @param file - The policy file to upload
   * @param fileName - Optional custom filename
   * @param accountHolderId - Account holder ID
   * @param auctionId - Related auction ID
   * @returns Upload result
   */
  static async uploadAuctionPolicyFile(
    file: File,
    fileName: string | undefined,
    accountHolderId: string,
    auctionId: string
  ): Promise<PolicyUploadResult> {
    return this.uploadPolicyFile({
      file,
      fileName,
      accountHolderId,
      documentType: "NEW_POLICY_DOCUMENT",
      relatedAuctionId: auctionId,
      isPolicyAttested: true,
      location: "policies"
    });
  }

  /**
   * Uploads a policy document for new policy creation
   * @param file - The policy file to upload
   * @param fileName - Optional custom filename
   * @param accountHolderId - Account holder ID
   * @returns Upload result
   */
  static async uploadNewPolicyFile(
    file: File,
    fileName: string | undefined,
    accountHolderId: string
  ): Promise<PolicyUploadResult> {
    return this.uploadPolicyFile({
      file,
      fileName,
      accountHolderId,
      documentType: "POLICY_DOCUMENT",
      isPolicyAttested: false,
      location: "policies"
    });
  }
}
